import time
from typing import Callable

fake_tokens = [
    200005,
    35644,
    200008,
    23483,
    316,
    1199,
    1114,
    717,
    170154,
    13,
    200007,
    200006,
    173781,
    200005,
    35644,
    316,
    28,
    44580,
    775,
    170154,
    464,
    91,
    542,
    141043,
    91,
    29,
    4108,
    200008,
    10848,
    7693,
    7534,
    28499,
    18826,
    18583,
    200012,
]
fake_tokens = [
    200005,
    35644,
    200008,
    1844,
    31064,
    25,
    392,
    4827,
    382,
    220,
    17,
    659,
    220,
    17,
    16842,
    12295,
    81645,
    13,
    51441,
    6052,
    13,
    200007,
    200006,
    173781,
    200005,
    17196,
    200008,
    17,
    659,
    220,
    17,
    314,
    220,
    19,
    13,
    9552,
    238,
    242,
    200002,
]
# fake_tokens = [200005, 35644, 200008, 976, 1825, 31064, 25, 392, 25216, 29400, 290, 11122, 306, 52768, 2117, 16842, 1416, 1309, 316, 2281, 198, 68, 290, 2208, 11122, 13, 1416, 679, 261, 1114, 717, 170154, 484, 44390, 261, 5100, 1621, 26, 581, 1757, 2005, 198, 75, 480, 483, 5100, 392, 137956, 2117, 11, 13180, 4050, 7801, 4733, 290, 11122, 5377, 484, 290, 1114, 7377, 13, 1416, 1309, 260, 198, 78, 1199, 290, 1114, 4584, 364, 58369, 2421, 717, 170154, 483, 5100, 392, 137956, 2117, 11, 13180, 4050, 200007, 200006, 173781, 200005, 12606, 815, 260, 198, 78, 28, 117673, 3490]
# fake_tokens = [
#     198,
#     200005,
#     35644,
#     200008,
#     23483,
#     316,
#     1199,
#     1114,
#     717,
#     170154,
#     13,
#     200007,
#     200006,
#     173781,
#     200005,
#     12606,
#     815,
#     316,
#     32455,
#     106847,
#     316,
#     28,
#     44580,
#     775,
#     170154,
#     464,
#     91,
#     542,
#     141043,
#     91,
#     29,
#     4108,
#     200008,
#     10848,
#     7693,
#     7534,
#     28499,
#     18826,
#     18583,
#     200012,
#     198,
# ]

token_queue = fake_tokens.copy()


def stub_infer_next_token(
    tokens: list[int], temperature: float = 0.0, new_request: bool = False
) -> int:
    global token_queue
    next_tok = token_queue.pop(0)
    if len(token_queue) == 0:
        token_queue = fake_tokens.copy()
    time.sleep(0.1)
    return next_tok


def setup_model(_checkpoint: str) -> Callable[[list[int], float], int]:
    return stub_infer_next_token
