name: CI

on:
  release:
    types: [published]
  push:
    tags:
      - "v*"
  workflow_dispatch:

# Minimal repo-level permissions; job-level permissions override where needed.
permissions:
  contents: read
  id-token: write

jobs:
  publish:
    name: Build & Publish to PyPI (Trusted Publishing)
    runs-on: ubuntu-latest

    # Run in the GitHub environment named "release" so you can gate it with approvals.
    environment: release

    # Extra permissions required for pypa action to do OIDC exchange:
    permissions:
      contents: read
      id-token: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.12"

      - name: Install build tools
        run: |
          python -m pip install --upgrade pip setuptools wheel build

      - name: Install uv (if needed)
        run: |
          python -m pip install --upgrade uv || true

      - name: Build package with uv
        run: |
          pwd
          ls -la
          uv build

      - name: Inspect dist folder
        run: |
          ls -la dist || ls -la build || echo "no dist/ or build/ — check uv output"

      - name: Publish to PyPI using Trusted Publishing
        # Note: No pypi_token / username / password provided — Trusted Publishing via OIDC is used.
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          attestations: true # optional (default for Trusted Publishing) - set to false to disable
